/* Unity USS Styles for Story Page - Visual Novel Layout */

/* Root Story Container */
.story-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    background-color: rgb(0, 0, 0);
}

/* Background Image */
.background-image {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: rgb(20, 20, 40);
}

/* Character Container */
.character-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: 200px;
}

/* Character Image */
.character-image {
    width: 400px;
    height: 600px;
    background-size: contain;
    background-position: center bottom;
    background-repeat: no-repeat;
    transition-duration: 0.5s;
    transition-property: opacity, translate;
}

/* UI Overlay */
.ui-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    justify-content: space-between;
    flex-direction: column;
}

/* Top UI Bar */
.top-bar {
    width: 100%;
    height: 60px;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20px;
    padding-top: 10px;
    background-color: rgba(0, 0, 0, 0.3);
}

.top-button {
    width: 80px;
    height: 40px;
    margin-left: 10px;
    background-color: rgba(50, 50, 50, 0.8);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 1px;
    border-radius: 5px;
    color: rgb(255, 255, 255);
    font-size: 14px;
    transition-duration: 0.2s;
}

.top-button:hover {
    background-color: rgba(80, 80, 80, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
    scale: 1.05;
}

.top-button:active {
    background-color: rgba(100, 100, 100, 0.9);
    scale: 0.95;
}

.top-button.active {
    background-color: rgba(100, 150, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);
    color: rgb(255, 255, 255);
}

/* Dialogue Area */
.dialogue-area {
    width: 100%;
    flex-direction: column;
    align-items: center;
    padding-bottom: 50px;
}

/* Character Name Box */
.name-box {
    width: 300px;
    height: 50px;
    background-color: rgba(30, 30, 60, 0.9);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 2px;
    border-radius: 10px 10px 0 0;
    justify-content: center;
    align-items: center;
    margin-bottom: -2px;
}

.character-name {
    color: rgb(255, 255, 255);
    font-size: 18px;
    font-style: bold;
    text-align: middle-center;
    -unity-text-align: middle-center;
}

/* Dialogue Box */
.dialogue-box {
    width: 90%;
    min-height: 150px;
    max-width: 1000px;
    background-color: rgba(20, 20, 40, 0.95);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 2px;
    border-radius: 15px;
    padding: 20px;
    position: relative;
    flex-direction: row;
    align-items: flex-end;
}

.dialogue-text {
    color: rgb(255, 255, 255);
    font-size: 16px;
    line-height: 24px;
    white-space: normal;
    flex-grow: 1;
    margin-right: 60px;
    -unity-text-align: upper-left;
}

/* Next Button */
.next-button {
    width: 50px;
    height: 50px;
    position: absolute;
    bottom: 15px;
    right: 15px;
    background-color: rgba(100, 150, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.5);
    border-width: 2px;
    border-radius: 25px;
    color: rgb(255, 255, 255);
    font-size: 20px;
    font-style: bold;
    transition-duration: 0.2s;
}

.next-button:hover {
    background-color: rgba(120, 170, 255, 0.9);
    scale: 1.1;
}

.next-button:active {
    background-color: rgba(80, 130, 255, 1);
    scale: 0.9;
}

/* Choices Container */
.choices-container {
    width: 90%;
    max-width: 800px;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
}

.choice-button {
    width: 100%;
    min-height: 50px;
    margin-bottom: 10px;
    background-color: rgba(60, 60, 100, 0.9);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 2px;
    border-radius: 10px;
    color: rgb(255, 255, 255);
    font-size: 16px;
    padding: 10px 20px;
    transition-duration: 0.2s;
    white-space: normal;
}

.choice-button:hover {
    background-color: rgba(80, 80, 120, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
    scale: 1.02;
}

.choice-button:active {
    background-color: rgba(100, 100, 140, 0.9);
    scale: 0.98;
}

/* Bottom UI Bar */
.bottom-bar {
    width: 100%;
    height: 60px;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.3);
    padding-bottom: 10px;
}

.bottom-button {
    width: 100px;
    height: 40px;
    margin-left: 10px;
    margin-right: 10px;
    background-color: rgba(50, 50, 50, 0.8);
    border-color: rgba(255, 255, 255, 0.3);
    border-width: 1px;
    border-radius: 5px;
    color: rgb(255, 255, 255);
    font-size: 14px;
    transition-duration: 0.2s;
}

.bottom-button:hover {
    background-color: rgba(80, 80, 80, 0.9);
    border-color: rgba(255, 255, 255, 0.5);
    scale: 1.05;
}

.bottom-button:active {
    background-color: rgba(100, 100, 100, 0.9);
    scale: 0.95;
}

/* Fade Overlay for Transitions */
.fade-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgb(0, 0, 0);
    opacity: 0;
    transition-duration: 0.5s;
    transition-property: opacity;
}

/* Animation Classes */
.fade-in {
    opacity: 1;
}

.fade-out {
    opacity: 0;
}

.slide-in-left {
    translate: -100px 0;
    opacity: 0;
}

.slide-in-right {
    translate: 100px 0;
    opacity: 0;
}

.slide-in-center {
    translate: 0 0;
    opacity: 1;
}

/* Character Position Classes */
.character-left {
    align-self: flex-start;
    margin-left: 100px;
}

.character-center {
    align-self: center;
}

.character-right {
    align-self: flex-end;
    margin-right: 100px;
}

/* Responsive Design */
@media (max-width: 800px) {
    .dialogue-box {
        width: 95%;
        padding: 15px;
    }
    
    .character-image {
        width: 300px;
        height: 450px;
    }
    
    .top-button, .bottom-button {
        width: 60px;
        font-size: 12px;
    }
}
