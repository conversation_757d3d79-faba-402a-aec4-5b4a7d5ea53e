# 📖 Story Page Layout Guide

## 🎭 Complete Visual Novel Interface

This guide explains the new comprehensive story page layout system with dialog box, background images, character positioning, and interactive UI elements.

---

## 📁 **FILES CREATED:**

### **UI Layout Files:**
- `UI/StoryPage.uxml` - Main layout structure
- `UI/StoryPage.uss` - Complete styling and animations
- `Resources/StoryPage.uxml` - Copy for runtime loading
- `Resources/StoryPage.uss` - Copy for runtime loading

### **Enhanced Scripts:**
- `Scripts/DialogueSystem.cs` - Updated with new UI elements
- `Scripts/StoryPageTester.cs` - Testing and demonstration script

---

## 🎨 **LAYOUT COMPONENTS:**

### **1. Background System**
```xml
<ui:VisualElement name="background-image" class="background-image" />
```
- **Full-screen background images**
- **Automatic loading from Resources/Background/**
- **Smooth transitions between scenes**
- **Cover scaling with center positioning**

### **2. Character Display**
```xml
<ui:VisualElement name="character-container" class="character-container">
    <ui:VisualElement name="character-image" class="character-image" />
</ui:VisualElement>
```
- **Dynamic character positioning (left, center, right)**
- **Smooth fade in/out transitions**
- **Automatic loading from Resources/characters/Maimy/**
- **Responsive character sizing**

### **3. Dialog System**
```xml
<ui:VisualElement name="dialogue-area" class="dialogue-area">
    <ui:VisualElement name="name-box" class="name-box">
        <ui:Label name="character-name" class="character-name" />
    </ui:VisualElement>
    <ui:VisualElement name="dialogue-box" class="dialogue-box">
        <ui:Label name="dialogue-text" class="dialogue-text" />
        <ui:Button name="next-button" class="next-button" />
    </ui:VisualElement>
</ui:VisualElement>
```
- **Character name plate with rounded corners**
- **Main dialog box with text animation**
- **Interactive next button with hover effects**
- **Choice system support**

### **4. Top UI Bar**
```xml
<ui:VisualElement name="top-bar" class="top-bar">
    <ui:Button name="menu-button" text="Menu" class="top-button" />
    <ui:Button name="auto-button" text="Auto" class="top-button" />
    <ui:Button name="skip-button" text="Skip" class="top-button" />
    <ui:Button name="log-button" text="Log" class="top-button" />
</ui:VisualElement>
```
- **Menu** - Game menu access
- **Auto** - Automatic dialogue progression
- **Skip** - Fast forward through text
- **Log** - Dialogue history

### **5. Bottom UI Bar**
```xml
<ui:VisualElement name="bottom-bar" class="bottom-bar">
    <ui:Button name="save-button" text="Save" class="bottom-button" />
    <ui:Button name="load-button" text="Load" class="bottom-button" />
    <ui:Button name="settings-button" text="Settings" class="bottom-button" />
</ui:VisualElement>
```
- **Save** - Save game state
- **Load** - Load saved games
- **Settings** - Game configuration

### **6. Choice System**
```xml
<ui:VisualElement name="choices-container" class="choices-container" />
```
- **Dynamic choice button generation**
- **Hover and click animations**
- **Automatic layout and spacing**

### **7. Transition Effects**
```xml
<ui:VisualElement name="fade-overlay" class="fade-overlay" />
```
- **Fade in/out transitions**
- **Scene change effects**
- **Smooth visual transitions**

---

## 🎮 **INTERACTIVE FEATURES:**

### **Button Hover Effects:**
- **Scale animations (1.05x on hover)**
- **Color transitions**
- **Border highlighting**
- **Smooth 0.2s transitions**

### **Character Positioning:**
```css
.character-left { align-self: flex-start; margin-left: 100px; }
.character-center { align-self: center; }
.character-right { align-self: flex-end; margin-right: 100px; }
```

### **Active States:**
```css
.top-button.active {
    background-color: rgba(100, 150, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);
}
```

---

## 🎯 **USAGE INSTRUCTIONS:**

### **1. Setup in Scene:**
```csharp
// Add to GameObject with UIDocument
var dialogueSystem = gameObject.AddComponent<DialogueSystem>();
var uiDocument = gameObject.GetComponent<UIDocument>();
uiDocument.visualTreeAsset = Resources.Load<VisualTreeAsset>("StoryPage");
```

### **2. Create Dialogue Data:**
```csharp
var dialogue = new DialogueData
{
    characterName = "Maimy",
    dialogueText = "Hello! Welcome to the story.",
    backgroundImage = "MaimyBackground",
    characterImage = "maimysenyummanis",
    characterPosition = "center",
    autoDelay = 3f
};
```

### **3. Character Positioning:**
- `"left"` - Character appears on left side
- `"center"` - Character appears in center (default)
- `"right"` - Character appears on right side

### **4. Available Images:**
**Backgrounds:** MaimyBackground, kota, maimystore1-4, taman paradise (1-3)
**Characters:** maimysenyummanis, maimycuriouscute, maimyketawakecil, maimymerenungmata

---

## 🔧 **TESTING:**

### **Use StoryPageTester:**
```csharp
// Add StoryPageTester component to test the layout
var tester = gameObject.AddComponent<StoryPageTester>();
tester.TestLayout(); // Creates sample dialogue with all features
```

### **Keyboard Controls:**
- **Space/Enter** - Advance dialogue
- **A** - Toggle auto mode
- **T** - Test layout (when StoryPageTester is present)

---

## 📱 **RESPONSIVE DESIGN:**

### **Mobile Support:**
```css
@media (max-width: 800px) {
    .dialogue-box { width: 95%; padding: 15px; }
    .character-image { width: 300px; height: 450px; }
    .top-button, .bottom-button { width: 60px; font-size: 12px; }
}
```

---

## 🎨 **CUSTOMIZATION:**

### **Colors and Themes:**
- **Dialog Box:** `rgba(20, 20, 40, 0.95)` - Dark blue with transparency
- **Name Box:** `rgba(30, 30, 60, 0.9)` - Slightly lighter blue
- **Buttons:** `rgba(50, 50, 50, 0.8)` - Dark gray with transparency
- **Active State:** `rgba(100, 150, 255, 0.8)` - Blue highlight

### **Animations:**
- **Transition Duration:** 0.2s for buttons, 0.5s for characters
- **Hover Scale:** 1.05x
- **Active Scale:** 0.95x
- **Fade Transitions:** 0.5s opacity changes

---

## ✅ **FEATURES COMPLETED:**

- ✅ **Full-screen background system**
- ✅ **Character positioning and transitions**
- ✅ **Interactive dialog box with name plate**
- ✅ **Complete UI button system**
- ✅ **Choice system with dynamic buttons**
- ✅ **Hover and click animations**
- ✅ **Responsive design**
- ✅ **Fade transition system**
- ✅ **Auto mode with visual feedback**
- ✅ **Keyboard shortcuts**
- ✅ **Resource loading system**

---

## 🚀 **NEXT STEPS:**

1. **Test the layout** using StoryPageTester
2. **Customize colors** in StoryPage.uss
3. **Add more character images** to Resources/characters/Maimy/
4. **Add more backgrounds** to Resources/Background/
5. **Implement save/load functionality**
6. **Add sound effects and music**
7. **Create menu system**
8. **Add dialogue history log**

The story page layout is now complete and ready for your visual novel! 🎭✨
