using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;

[System.Serializable]
public class DialogueData
{
    public string characterName;
    [TextArea(3, 10)]
    public string dialogueText;
    public string backgroundImage;
    public string characterImage;
    public string characterPosition = "center";
    public float autoDelay = 3f;
    public string audioClip;
    public bool isChoice = false;
    public string[] choices;
    public int[] choiceTargets;
}

public class DialogueSystem : MonoBehaviour
{
    [Header("Dialogue Data")]
    public DialogueData[] dialogues;
    
    [Header("Settings")]
    public float textSpeed = 0.05f;
    public bool autoMode = false;
    
    private UIDocument uiDocument;
    private VisualElement root;
    private Label dialogueText;
    private Label characterName;
    private Button nextButton;
    private VisualElement choicesContainer;
    private VisualElement backgroundImage;
    private VisualElement characterImage;

    // Additional UI elements for enhanced layout
    private Button autoButton;
    private Button skipButton;
    private Button menuButton;
    private Button logButton;
    private Button saveButton;
    private Button loadButton;
    private Button settingsButton;
    private VisualElement fadeOverlay;
    
    private int currentDialogueIndex = 0;
    private bool isTyping = false;
    private Coroutine typingCoroutine;
    private Coroutine autoCoroutine;

    private void OnEnable()
    {
        // Delay initialization to ensure UI is ready
        StartCoroutine(DelayedInitialization());
    }

    private IEnumerator DelayedInitialization()
    {
        // Wait a frame to ensure UIDocument is fully loaded
        yield return null;
        InitializeUI();
    }

    private void InitializeUI()
    {
        uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            Debug.LogError("UIDocument component not found! Please add UIDocument component and assign StoryPage.uxml");
            return;
        }

        if (uiDocument.rootVisualElement == null)
        {
            Debug.LogError("UIDocument root visual element is null! Make sure StoryPage.uxml is assigned to UIDocument");
            return;
        }

        root = uiDocument.rootVisualElement;

        // Safely get UI elements with null checks
        dialogueText = root.Q<Label>("dialogue-text");
        characterName = root.Q<Label>("character-name");
        nextButton = root.Q<Button>("next-button");
        choicesContainer = root.Q("choices-container");
        backgroundImage = root.Q("background-image");
        characterImage = root.Q("character-image");

        // Get additional UI elements
        autoButton = root.Q<Button>("auto-button");
        skipButton = root.Q<Button>("skip-button");
        menuButton = root.Q<Button>("menu-button");
        logButton = root.Q<Button>("log-button");
        saveButton = root.Q<Button>("save-button");
        loadButton = root.Q<Button>("load-button");
        settingsButton = root.Q<Button>("settings-button");
        fadeOverlay = root.Q("fade-overlay");

        // Validate critical UI elements
        if (dialogueText == null)
            Debug.LogError("dialogue-text Label not found in UXML!");
        if (characterName == null)
            Debug.LogError("character-name Label not found in UXML!");
        if (nextButton == null)
            Debug.LogError("next-button Button not found in UXML!");

        // Register button callbacks safely
        if (nextButton != null)
            nextButton.clicked += OnNextButtonClicked;
        if (autoButton != null)
            autoButton.clicked += ToggleAutoMode;
        if (skipButton != null)
            skipButton.clicked += OnSkipButtonClicked;
        if (menuButton != null)
            menuButton.clicked += OnMenuButtonClicked;
        if (logButton != null)
            logButton.clicked += OnLogButtonClicked;
        if (saveButton != null)
            saveButton.clicked += OnSaveButtonClicked;
        if (loadButton != null)
            loadButton.clicked += OnLoadButtonClicked;
        if (settingsButton != null)
            settingsButton.clicked += OnSettingsButtonClicked;
    }

    public void StartDialogue()
    {
        if (dialogues == null || dialogues.Length == 0)
        {
            Debug.LogError("No dialogue data available! Please assign dialogue data to start the story.");

            // Try to load default story data
            var defaultStoryData = Resources.Load<SampleStoryData>("MaimyStoryData");
            if (defaultStoryData != null && defaultStoryData.dialogues != null && defaultStoryData.dialogues.Length > 0)
            {
                dialogues = defaultStoryData.dialogues;
                Debug.Log("Loaded default story data from Resources/MaimyStoryData");
            }
            else
            {
                Debug.LogError("No default story data found! Create a SampleStoryData asset or assign dialogue data manually.");
                return;
            }
        }

        currentDialogueIndex = 0;
        ShowDialogue(dialogues[0]);
    }

    public void OnNextButtonClicked()
    {
        if (dialogues == null || dialogues.Length == 0)
        {
            Debug.LogError("No dialogue data available!");
            return;
        }

        if (currentDialogueIndex < 0 || currentDialogueIndex >= dialogues.Length)
        {
            Debug.LogError($"Dialogue index {currentDialogueIndex} is out of range! Max index: {dialogues.Length - 1}");
            currentDialogueIndex = 0; // Reset to start
            return;
        }

        if (isTyping)
        {
            // Skip typing animation
            if (typingCoroutine != null)
            {
                StopCoroutine(typingCoroutine);
                isTyping = false;
                if (currentDialogueIndex < dialogues.Length)
                    dialogueText.text = dialogues[currentDialogueIndex].dialogueText;
            }
        }
        else
        {
            NextDialogue();
        }
    }

    private void NextDialogue()
    {
        if (dialogues == null || dialogues.Length == 0)
        {
            Debug.LogError("No dialogue data available!");
            return;
        }

        currentDialogueIndex++;
        if (currentDialogueIndex >= 0 && currentDialogueIndex < dialogues.Length)
        {
            ShowDialogue(dialogues[currentDialogueIndex]);
        }
        else
        {
            Debug.Log("Story completed!");
            currentDialogueIndex = dialogues.Length - 1; // Keep at last valid index
        }
    }

    private void ShowDialogue(DialogueData dialogue)
    {
        if (dialogue == null)
        {
            Debug.LogError("Dialogue data is null!");
            return;
        }

        // Safely update UI elements
        if (characterName != null)
            characterName.text = dialogue.characterName ?? "";

        if (dialogueText != null)
        {
            if (typingCoroutine != null)
                StopCoroutine(typingCoroutine);

            typingCoroutine = StartCoroutine(TypeText(dialogue.dialogueText ?? ""));
        }

        // Handle choices
        if (dialogue.isChoice && dialogue.choices != null && dialogue.choices.Length > 0)
        {
            ShowChoices(dialogue);
        }
        else
        {
            HideChoices();
        }

        LoadBackgroundImage(dialogue.backgroundImage);
        LoadCharacterImage(dialogue.characterImage);
        SetCharacterPosition(dialogue.characterPosition);
    }

    private void ShowChoices(DialogueData dialogue)
    {
        if (choicesContainer == null) return;

        // Clear existing choices
        choicesContainer.Clear();
        choicesContainer.style.display = DisplayStyle.Flex;

        // Create choice buttons
        for (int i = 0; i < dialogue.choices.Length; i++)
        {
            if (i >= dialogue.choices.Length) break; // Extra safety check

            var choiceButton = new Button();
            choiceButton.text = dialogue.choices[i];
            choiceButton.AddToClassList("choice-button");

            // Capture the index for the closure
            int choiceIndex = i;
            choiceButton.clicked += () => OnChoiceSelected(dialogue, choiceIndex);

            choicesContainer.Add(choiceButton);
        }
    }

    private void HideChoices()
    {
        if (choicesContainer != null)
        {
            choicesContainer.style.display = DisplayStyle.None;
            choicesContainer.Clear();
        }
    }

    private void OnChoiceSelected(DialogueData dialogue, int choiceIndex)
    {
        // Validate choice index and targets
        if (dialogue.choiceTargets == null ||
            choiceIndex < 0 ||
            choiceIndex >= dialogue.choiceTargets.Length ||
            choiceIndex >= dialogue.choices.Length)
        {
            Debug.LogError($"Invalid choice selection! Choice index: {choiceIndex}, Targets length: {dialogue.choiceTargets?.Length ?? 0}");
            return;
        }

        int targetIndex = dialogue.choiceTargets[choiceIndex];

        // Validate target index
        if (targetIndex < 0 || targetIndex >= dialogues.Length)
        {
            Debug.LogError($"Invalid choice target! Target: {targetIndex}, Dialogues length: {dialogues.Length}");
            return;
        }

        // Hide choices and jump to target dialogue
        HideChoices();
        currentDialogueIndex = targetIndex;
        ShowDialogue(dialogues[currentDialogueIndex]);
    }

    private IEnumerator TypeText(string text)
    {
        isTyping = true;
        dialogueText.text = "";
        
        foreach (char c in text)
        {
            dialogueText.text += c;
            yield return new WaitForSeconds(textSpeed);
        }
        
        isTyping = false;
        
        if (autoMode)
        {
            autoCoroutine = StartCoroutine(AutoAdvance());
        }
    }

    private IEnumerator AutoAdvance()
    {
        if (dialogues != null && currentDialogueIndex < dialogues.Length)
        {
            yield return new WaitForSeconds(dialogues[currentDialogueIndex].autoDelay);
            NextDialogue();
        }
    }

    public void ToggleAutoMode()
    {
        autoMode = !autoMode;
        Debug.Log($"Auto mode: {(autoMode ? "ON" : "OFF")}");

        // Update button appearance
        if (autoButton != null)
        {
            if (autoMode)
                autoButton.AddToClassList("active");
            else
                autoButton.RemoveFromClassList("active");
        }
    }

    private void OnSkipButtonClicked()
    {
        Debug.Log("Skip button clicked - Fast forward through dialogue");
        // TODO: Implement skip functionality
    }

    private void OnMenuButtonClicked()
    {
        Debug.Log("Menu button clicked - Show game menu");
        // TODO: Implement menu functionality
    }

    private void OnLogButtonClicked()
    {
        Debug.Log("Log button clicked - Show dialogue history");
        // TODO: Implement dialogue log functionality
    }

    private void OnSaveButtonClicked()
    {
        Debug.Log("Save button clicked - Save game state");
        // TODO: Implement save functionality
    }

    private void OnLoadButtonClicked()
    {
        Debug.Log("Load button clicked - Load game state");
        // TODO: Implement load functionality
    }

    private void OnSettingsButtonClicked()
    {
        Debug.Log("Settings button clicked - Show settings menu");
        // TODO: Implement settings functionality
    }

    private void LoadBackgroundImage(string imageName)
    {
        if (backgroundImage == null || string.IsNullOrEmpty(imageName))
            return;

        // Try to load from Resources/Background folder
        Texture2D texture = Resources.Load<Texture2D>($"Background/{imageName}");
        if (texture != null)
        {
            backgroundImage.style.backgroundImage = new StyleBackground(texture);
            Debug.Log($"Loaded background image: {imageName}");
        }
        else
        {
            Debug.LogWarning($"Background image not found: Background/{imageName}");
        }
    }

    private void LoadCharacterImage(string imageName)
    {
        if (characterImage == null || string.IsNullOrEmpty(imageName))
        {
            // Hide character image if no name provided
            if (characterImage != null)
            {
                characterImage.style.backgroundImage = StyleKeyword.None;
                characterImage.style.opacity = 0;
            }
            return;
        }

        // Try to load from Resources/characters/Maimy folder
        Texture2D texture = Resources.Load<Texture2D>($"characters/Maimy/{imageName}");
        if (texture != null)
        {
            characterImage.style.backgroundImage = new StyleBackground(texture);
            characterImage.style.opacity = 1;
            Debug.Log($"Loaded character image: {imageName}");
        }
        else
        {
            Debug.LogWarning($"Character image not found: characters/Maimy/{imageName}");
        }
    }

    private void SetCharacterPosition(string position)
    {
        if (characterImage == null) return;

        // Remove all position classes
        characterImage.RemoveFromClassList("character-left");
        characterImage.RemoveFromClassList("character-center");
        characterImage.RemoveFromClassList("character-right");

        // Add the appropriate position class
        switch (position?.ToLower())
        {
            case "left":
                characterImage.AddToClassList("character-left");
                break;
            case "right":
                characterImage.AddToClassList("character-right");
                break;
            case "center":
            default:
                characterImage.AddToClassList("character-center");
                break;
        }

        Debug.Log($"Character positioned: {position}");
    }

    private void OnDisable()
    {
        // Clean up event subscriptions to prevent memory leaks
        if (nextButton != null)
            nextButton.clicked -= OnNextButtonClicked;
        if (autoButton != null)
            autoButton.clicked -= ToggleAutoMode;
        if (skipButton != null)
            skipButton.clicked -= OnSkipButtonClicked;
        if (menuButton != null)
            menuButton.clicked -= OnMenuButtonClicked;
        if (logButton != null)
            logButton.clicked -= OnLogButtonClicked;
        if (saveButton != null)
            saveButton.clicked -= OnSaveButtonClicked;
        if (loadButton != null)
            loadButton.clicked -= OnLoadButtonClicked;
        if (settingsButton != null)
            settingsButton.clicked -= OnSettingsButtonClicked;

        // Stop any running coroutines
        if (typingCoroutine != null)
        {
            StopCoroutine(typingCoroutine);
            typingCoroutine = null;
        }

        if (autoCoroutine != null)
        {
            StopCoroutine(autoCoroutine);
            autoCoroutine = null;
        }
    }

    // Public method to safely set dialogue data
    public void SetDialogueData(DialogueData[] newDialogues)
    {
        if (newDialogues == null || newDialogues.Length == 0)
        {
            Debug.LogWarning("Attempted to set null or empty dialogue data");
            return;
        }

        dialogues = newDialogues;
        currentDialogueIndex = 0;
        Debug.Log($"Dialogue data set successfully. {dialogues.Length} dialogues loaded.");
    }

    // Public method to get current dialogue info (useful for debugging)
    public string GetCurrentDialogueInfo()
    {
        if (dialogues == null || dialogues.Length == 0)
            return "No dialogue data";

        return $"Dialogue {currentDialogueIndex + 1}/{dialogues.Length}: {dialogues[currentDialogueIndex]?.characterName ?? "Unknown"}";
    }
}




